import Decimal from 'decimal.js';
import React, { useEffect, useState, useCallback } from 'react'
import { EntityWithDetails } from '~/utils/db/entities/entities.db.server';
import { RowWithValues, RowValueWithDetails } from '~/utils/db/entities/rows.db.server';
import CircleStepper from '../CircleStepper/CircleStepper';
import clsx from 'clsx';
import { Link } from 'react-router';
import { RowValue } from '@prisma/client';
import { PropertyType } from '~/application/enums/entities/PropertyType';
import DateUtils from '~/utils/shared/DateUtils';
import MediaItem from '~/components/ui/uploaders/MediaItem';
import KeywordTags from '../RowOverviewRoute/components/KeywordTags';
import RatingBadge from '~/components/ui/badges/RatingBadge';
import { MediaDto } from '~/application/dtos/entities/MediaDto';
import PreviewMediaModal from '~/components/ui/media/PreviewMediaModal';

interface InfoItemProps {
  label: string;
  value: string | React.ReactNode;
  type: string;
}

interface TopbarProps {
  items: { label: string; value: string | React.ReactNode }[];
}

const Topbar: React.FC<TopbarProps> = ({ items }) => {
  const InfoItem: React.FC<InfoItemProps> = ({ label, value,type }) => {
    console.log(value,type,)
    return (
      <div className="flex flex-col gap-4 w-full max-md:max-w-full ">
        <div className="flex flex-row gap-4 items-baseline">
          <div className="w-48 text-xs text-gray-500">
            <span className="text-xs">{label}</span>
          </div>
          <div className="text-sm font-medium text-neutral-800 break-words flex-1">
            {value}
          </div>
        </div>
      </div>
    );
  };
function determineType(item: any) {
if(item.type === PropertyType.MEDIA){
  return "media";
}
if(item.type === PropertyType.MULTI_TEXT){
  return "multiText";
}
return "text";

}

 console.log("items",items)
  return (
    <header className="flex flex-col justify-center py-5 rounded-lg w-full max-w-6xl">
      <div className="w-full max-md:max-w-full">
        <div className={clsx(
          "grid grid-cols-2 gap-2 items-center",
      
        )}>
         {items.map((item, index) => {
  // Determine the type based on your logic
  const type = determineType(item); // You'll need to implement this function
  
  return (
    <InfoItem
      key={index}
      label={item.label}
      value={item.value}
      type={type}
    />
  );
})}
        </div>
      </div>
    </header>
  );
};


const OverViewSecondaryHeader = ({ entity, item, rowData }: { entity: EntityWithDetails; item: RowWithValues; rowData: any }) => {
  const [secondaryHeader, setSecondaryHeader] = useState<{ label: string; value: string | React.ReactNode }[]>([]);
  const [selectedItem, setSelectedItem] = useState<MediaDto>();
  const getValueByType = useCallback(({
    type,
    subType,
    rowValue,
    attributes,
    label,
  }: {
    type: number;
    subType: string | null;
    rowValue: RowValueWithDetails;
    attributes: any;
    label?: string;
  }): any => {
    // Helper functions for formatting different value types
      
    const getFormattedTextValue = (subType: string | null, value: string, editorType: string | null, id: string, label?: string) => {
      const naClassName = "!text-foreground !font-medium !text-[14px] pl-[2px]";
      if (!value) return <span className={naClassName}>N/A</span>;
      const className = "inline-block items-center gap-5 text-sm text-primary my-auto self-stretch underline decoration-solid decoration-auto underline-offset-auto";
      if (value === "N/A") return <span className={naClassName}>N/A</span>;
      switch (subType) {
        case "email":
          return (
            <div className="flex items-center gap-3 text-sm text-primary group">
              <div>
                <a href={`mailto:${value}`} className={className}>
                  {value}
                </a>
              </div>
            </div>
          );
        case "url":
          return (
            <div className="flex items-center gap-5 text-sm text-primary">
              <Link
                to={value}
                rel="noreferrer"
                target="_blank"
                className={className}
              >
                {label ? `View ${label}` : "View"}
              </Link>
            </div>
          );
        default:
          return <div className="break-words">{value}</div>;
      }
    };
      function download(item: MediaDto) {
    const downloadLink = document.createElement("a");
    downloadLink.href = item.publicUrl ?? item.file;
    downloadLink.download = item.name;
    downloadLink.click();
  }
    const getFormattedMultiText = (rowValue: RowValueWithDetails) => {
      const naClassName = "!text-foreground !font-medium !text-[14px] pl-[2px]";
      if (!rowValue?.multiple?.length) return <span className={naClassName}>N/A</span>;

      let textChips = rowValue?.multiple?.map((o: any) => o.value);
      return <KeywordTags tags={textChips} type={rowValue.multiple?.length == 0 ? "single" : "multiple"}/>;
    };

    const getFormattedSingleSelect = (rowValue: RowValueWithDetails) => {
      const naClassName = "!text-foreground !font-medium !text-[14px] pl-[2px]";
      if (!rowValue?.textValue) return <span className={naClassName}>N/A</span>;
      return <KeywordTags tags={[rowValue?.textValue]} type={rowValue.multiple?.length === 0 ? "single" : "multiple"}/>;
    };
    
      function preview(item: MediaDto) {
        setSelectedItem(item);
      }

    const getFormattedMultiple = (rowValue: RowValueWithDetails) => {
      const naClassName = "!text-foreground !font-medium !text-[14px] pl-[2px]";
      if (!rowValue?.multiple?.length) return <span className={naClassName}>N/A</span>;

      let multitextValues = rowValue?.multiple?.map((item) => item.value);
      return <KeywordTags tags={multitextValues} />;
    };
    const isRating = attributes?.find((item: any) => item.value === "rating") ? true : false;

    if (isRating) {
      return (
        <span>
          <RatingBadge value={Number(rowValue?.numberValue)} />
        </span>
      );
    }

    switch (type) {
      case PropertyType.TEXT:
        const editorType = attributes.find((item: any) => item.name === "editor")?.value;
        return getFormattedTextValue(subType, rowValue?.textValue || "N/A", editorType, rowValue?.id, label);

      case PropertyType.BOOLEAN:
        return typeof rowValue?.booleanValue === "boolean" ? (rowValue.booleanValue ? "Yes" : "No") : "N/A";

      case PropertyType.NUMBER:
        return rowValue?.numberValue || "N/A";

      case PropertyType.DATE:
        return rowValue?.dateValue ? DateUtils.dateMonthDayYear(rowValue.dateValue) : "N/A";
      case PropertyType.TIME:
        if (!rowValue?.dateValue) return "N/A";
        const is12HourFormat = subType === "12h";
        return is12HourFormat ? DateUtils.timeHM12(rowValue.dateValue) : DateUtils.timeHM(rowValue.dateValue);
      case PropertyType.DATE_TIME:
        if (!rowValue?.dateValue) return "N/A";
        const is12HourFormatDateTime = subType === "12h";
        const dateStr = DateUtils.dateMonthDayYear(rowValue.dateValue);
        const timeStr = is12HourFormatDateTime ? DateUtils.timeHM12(rowValue.dateValue) : DateUtils.timeHM(rowValue.dateValue);
        return `${dateStr} ${timeStr}`;
      case PropertyType.RANGE_DATE:
        const minDate = rowValue?.range?.dateMin ? DateUtils.dateMonthDayYear(rowValue?.range?.dateMin) : "N/A";
        const maxDate = rowValue?.range?.dateMax ? DateUtils.dateMonthDayYear(rowValue?.range?.dateMax) : "N/A";
        return `${minDate} - ${maxDate}`;

      case PropertyType.TIME_RANGE:
        if (!rowValue?.range?.dateMin || !rowValue?.range?.dateMax) return "N/A";

        const startDate = rowValue.range.dateMin instanceof Date ? rowValue.range.dateMin : new Date(rowValue.range.dateMin);
        const endDate = rowValue.range.dateMax instanceof Date ? rowValue.range.dateMax : new Date(rowValue.range.dateMax);

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return "N/A";

        const is12HourFormatTimeRange = subType === "12h";
        const startTime = is12HourFormatTimeRange ? DateUtils.timeHM12(startDate) : DateUtils.timeHM(startDate);
        const endTime = is12HourFormatTimeRange ? DateUtils.timeHM12(endDate) : DateUtils.timeHM(endDate);

        const startMinutes = startDate.getHours() * 60 + startDate.getMinutes();
        const endMinutes = endDate.getHours() * 60 + endDate.getMinutes();
        const isOvernight = endMinutes <= startMinutes;

        return isOvernight ? `${startTime} – ${endTime} (+1 day)` : `${startTime} – ${endTime}`;

      case PropertyType.MEDIA:
        return rowValue?.media ? (
          <MediaItem
            item={{
              id: rowValue?.media?.[0]?.id || "",
              title: rowValue?.media?.[0]?.title || "",
              name: rowValue?.media?.[0]?.name || "",
              file: rowValue?.media?.[0]?.file || "",
              type: rowValue?.media?.[0]?.type || "",
              publicUrl: rowValue?.media?.[0]?.publicUrl || undefined,
              storageBucket: rowValue?.media?.[0]?.storageBucket || undefined,
              storageProvider: rowValue?.media?.[0]?.storageProvider || undefined,
            }}
           onChangeTitle={function (e: string): void {
                throw new Error("Function not implemented.");
              }}
             onDelete={function (): void {
                throw new Error("Function not implemented.");
              }}
            onDownload={() => {
                const mediaItem = rowValue?.media?.[0];
                if (mediaItem) {
                  download(mediaItem);
                } else {
                  console.error("Media item is missing required properties.");
                }
              }}
            onPreview={() => {
                const mediaItem = rowValue?.media?.[0];
                if (mediaItem) {
                  preview(mediaItem);
                } else {
                  console.error("Media item is missing required properties.");
                }
              }}
            readOnly={true}
          />
        ) : (
          "N/A"
        );

      case PropertyType.MULTI_SELECT:
        return getFormattedMultiple(rowValue);
      case PropertyType.MULTI_TEXT:
        return getFormattedMultiText(rowValue);

      case PropertyType.RANGE_NUMBER:
        return rowValue?.range?.numberMin && rowValue?.range?.numberMax ? `${rowValue?.range?.numberMin} - ${rowValue?.range?.numberMax}` : "N/A";
      case PropertyType.SELECT:
        return getFormattedSingleSelect(rowValue);
      case PropertyType.LOCATION:
        return rowValue?.textValue ? JSON.parse(rowValue?.textValue)?.formattedAddress || JSON.parse(rowValue?.textValue)?.address || "Location data" : "No location data";

      default:
        return "Type N/A";
    }
  }, []);

  useEffect(() => {
    function getTitleFromProperty(overviewHeaderPropertyId: string, item: RowWithValues) {
      const val = item.values.find((v) => v.propertyId === overviewHeaderPropertyId);
      const property = entity.properties.find((p) => p.id === overviewHeaderPropertyId);
      if (!property || !val) return "N/A";

      return getValueByType({
        type: property.type,
        subType: property.subtype,
        rowValue: val,
        attributes: property.attributes || [],
        label: property.title || property.name
      });
    }

    const overViewSecondaryHeaderProperties = entity.properties.filter((p) => p.isOverviewSecondaryHeaderProperty);
    const mapped = overViewSecondaryHeaderProperties.map((property) => {
      return {
        label: property.title || property.name,
        value: getTitleFromProperty(property.id, item),
        type: property.type,
        subtype: property.subtype,
      };
    });
    setSecondaryHeader(mapped);
  }, [entity, item, getValueByType]);
  return (
    (secondaryHeader.length > 0 || rowData?.entity?.isStepFormWizard) && (
      <div className="flex justify-between items-center w-full bg-card">
        {secondaryHeader.length > 0 && <Topbar items={secondaryHeader} />}
        {selectedItem && <PreviewMediaModal item={selectedItem} onClose={() => setSelectedItem(undefined)} onDownload={() => download(selectedItem)} />}
      </div>
    )
  );
};

export default OverViewSecondaryHeader;